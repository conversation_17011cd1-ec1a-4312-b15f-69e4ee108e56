import React from 'react';
import { Clock, Users, ChefHat } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';

export interface Recipe {
  id: string;
  name: string;
  description: string;
  cookTime: string;
  servings: number;
  difficulty: 'Easy' | 'Medium' | 'Hard';
  ingredients: string[];
  instructions: string[];
  tags: string[];
}

interface RecipeCardProps {
  recipe: Recipe;
  onSelect: (recipe: Recipe) => void;
}

export const RecipeCard: React.FC<RecipeCardProps> = ({ recipe, onSelect }) => {
  const difficultyColors = {
    Easy: 'bg-green-fresh text-green-fresh-foreground',
    Medium: 'bg-orange-light text-orange-warm',
    Hard: 'bg-destructive text-destructive-foreground'
  };

  return (
    <Card className="group hover:shadow-warm transition-all duration-300 cursor-pointer transform hover:scale-105 bg-gradient-card border-border/50 overflow-hidden">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <CardTitle className="text-lg mb-2 group-hover:text-primary transition-colors">
              {recipe.name}
            </CardTitle>
            <CardDescription className="text-muted-foreground line-clamp-2">
              {recipe.description}
            </CardDescription>
          </div>
          <Badge 
            className={`ml-2 ${difficultyColors[recipe.difficulty]} border-0 shadow-soft`}
          >
            {recipe.difficulty}
          </Badge>
        </div>
      </CardHeader>
      
      <CardContent className="pt-0">
        <div className="flex items-center gap-4 text-sm text-muted-foreground mb-4">
          <div className="flex items-center gap-1">
            <Clock className="h-4 w-4" />
            <span>{recipe.cookTime}</span>
          </div>
          <div className="flex items-center gap-1">
            <Users className="h-4 w-4" />
            <span>{recipe.servings} servings</span>
          </div>
          <div className="flex items-center gap-1">
            <ChefHat className="h-4 w-4" />
            <span>{recipe.ingredients.length} ingredients</span>
          </div>
        </div>

        <div className="flex flex-wrap gap-1 mb-4">
          {recipe.tags.slice(0, 3).map((tag) => (
            <Badge 
              key={tag} 
              variant="secondary" 
              className="text-xs bg-accent/50 text-accent-foreground border-0"
            >
              {tag}
            </Badge>
          ))}
          {recipe.tags.length > 3 && (
            <Badge 
              variant="secondary" 
              className="text-xs bg-accent/50 text-accent-foreground border-0"
            >
              +{recipe.tags.length - 3} more
            </Badge>
          )}
        </div>

        <Button 
          onClick={() => onSelect(recipe)}
          className="w-full"
          variant="hero"
        >
          View Recipe
        </Button>
      </CardContent>
    </Card>
  );
};