@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. 
All colors MUST be HSL.
*/

@layer base {
  :root {
    --background: 35 20% 98%;
    --foreground: 25 15% 15%;

    --card: 0 0% 100%;
    --card-foreground: 25 15% 15%;

    --popover: 0 0% 100%;
    --popover-foreground: 25 15% 15%;

    --primary: 25 95% 53%;
    --primary-foreground: 0 0% 100%;

    --secondary: 120 40% 85%;
    --secondary-foreground: 25 15% 15%;

    --muted: 35 15% 95%;
    --muted-foreground: 25 15% 50%;

    --accent: 35 80% 88%;
    --accent-foreground: 25 15% 15%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 100%;

    --border: 35 20% 90%;
    --input: 35 20% 95%;
    --ring: 25 95% 53%;

    --radius: 0.75rem;

    /* Custom food-inspired colors */
    --orange-warm: 25 95% 53%;
    --orange-light: 35 80% 88%;
    --green-fresh: 120 40% 85%;
    --cream-soft: 35 20% 98%;
    
    /* Gradients */
    --gradient-warm: linear-gradient(135deg, hsl(var(--orange-warm)), hsl(var(--orange-light)));
    --gradient-hero: linear-gradient(135deg, hsl(var(--orange-warm)) 0%, hsl(35 70% 75%) 100%);
    --gradient-card: linear-gradient(145deg, hsl(0 0% 100%) 0%, hsl(35 15% 98%) 100%);
    
    /* Shadows */
    --shadow-warm: 0 10px 30px -10px hsl(var(--orange-warm) / 0.3);
    --shadow-soft: 0 4px 20px -2px hsl(25 30% 60% / 0.15);
    
    /* Animations */
    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-bounce: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 25 15% 8%;
    --foreground: 35 20% 95%;

    --card: 25 15% 12%;
    --card-foreground: 35 20% 95%;

    --popover: 25 15% 12%;
    --popover-foreground: 35 20% 95%;

    --primary: 25 95% 58%;
    --primary-foreground: 25 15% 8%;

    --secondary: 120 20% 25%;
    --secondary-foreground: 35 20% 95%;

    --muted: 25 10% 20%;
    --muted-foreground: 35 15% 65%;

    --accent: 25 10% 20%;
    --accent-foreground: 35 20% 95%;

    --destructive: 0 75% 55%;
    --destructive-foreground: 35 20% 95%;

    --border: 25 10% 20%;
    --input: 25 10% 20%;
    --ring: 25 95% 58%;

    /* Dark mode gradients */
    --gradient-warm: linear-gradient(135deg, hsl(var(--primary)), hsl(25 80% 45%));
    --gradient-hero: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(25 60% 35%) 100%);
    --gradient-card: linear-gradient(145deg, hsl(25 15% 12%) 0%, hsl(25 10% 15%) 100%);
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}