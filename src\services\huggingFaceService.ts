import { Recipe } from '../types/Recipe';

export class HuggingFaceService {
  private readonly baseUrl = 'https://api-inference.huggingface.co/models';
  
  constructor() {
    console.log('HuggingFaceService initialized (No API key required!)');
  }

  async analyzeImageAndGenerateRecipes(imageFile: File): Promise<Recipe[]> {
    try {
      console.log('=== HUGGING FACE API CALL START ===');
      console.log('Image file:', imageFile.name, imageFile.type, imageFile.size);

      // Convert image to base64
      const base64Image = await this.fileToBase64(imageFile);
      console.log('Image converted to base64, length:', base64Image.length);

      // Use Hugging Face's image-to-text model to analyze the image
      const imageDescription = await this.analyzeImage(base64Image);
      console.log('Image analysis result:', imageDescription);

      // Generate recipes based on the image description
      const recipes = await this.generateRecipesFromDescription(imageDescription);
      console.log('Generated recipes:', recipes.length);

      return recipes;
    } catch (error) {
      console.error('=== HUGGING FACE API ERROR ===');
      console.error('Error:', error);
      throw error;
    }
  }

  private async analyzeImage(base64Image: string): Promise<string> {
    try {
      // Convert base64 to blob for better API compatibility
      const base64Data = base64Image.split(',')[1];
      const byteCharacters = atob(base64Data);
      const byteNumbers = new Array(byteCharacters.length);
      for (let i = 0; i < byteCharacters.length; i++) {
        byteNumbers[i] = byteCharacters.charCodeAt(i);
      }
      const byteArray = new Uint8Array(byteNumbers);

      // Try multiple vision models for better accuracy
      const models = [
        'Salesforce/blip-image-captioning-large',
        'Salesforce/blip-image-captioning-base',
        'nlpconnect/vit-gpt2-image-captioning'
      ];

      for (const model of models) {
        try {
          console.log(`Trying image analysis with model: ${model}`);

          const response = await fetch(`${this.baseUrl}/${model}`, {
            method: 'POST',
            body: byteArray,
            headers: {
              'Content-Type': 'application/octet-stream',
            },
          });

          if (response.ok) {
            const result = await response.json();
            console.log(`Image analysis result from ${model}:`, result);

            // Extract the generated text
            if (Array.isArray(result) && result.length > 0 && result[0].generated_text) {
              const description = result[0].generated_text.toLowerCase();
              console.log('Extracted description:', description);
              return description;
            }
          } else if (response.status === 503) {
            console.log(`Model ${model} is loading, trying next...`);
            continue;
          }
        } catch (modelError) {
          console.log(`Error with model ${model}:`, modelError);
          continue;
        }
      }

      // If all models fail, return a generic description
      console.log('All image analysis models failed, using fallback');
      return 'various food ingredients and cooking items';
    } catch (error) {
      console.error('Error analyzing image:', error);
      return 'food ingredients and cooking items';
    }
  }

  private async generateRecipesFromDescription(description: string): Promise<Recipe[]> {
    try {
      const prompt = `Based on the image showing "${description}", generate 3 detailed recipes. 

Format each recipe as JSON with this structure:
{
  "name": "Recipe Name",
  "description": "Brief description",
  "ingredients": ["ingredient 1", "ingredient 2"],
  "instructions": ["step 1", "step 2"],
  "prepTime": "15 minutes",
  "cookTime": "30 minutes",
  "servings": 4,
  "difficulty": "Easy"
}

Generate 3 different recipes based on the ingredients or food shown in the image:`;

      // Use Microsoft's DialoGPT for text generation
      const response = await fetch(`${this.baseUrl}/microsoft/DialoGPT-large`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          inputs: prompt,
          parameters: {
            max_length: 1000,
            temperature: 0.7,
            do_sample: true,
          },
        }),
      });

      if (!response.ok) {
        // If the model is loading, try a simpler approach
        if (response.status === 503) {
          console.log('Model is loading, generating fallback recipes...');
          return this.generateFallbackRecipes(description);
        }
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      console.log('Text generation result:', result);

      // Try to parse the generated recipes
      try {
        return this.parseGeneratedRecipes(result, description);
      } catch (parseError) {
        console.log('Failed to parse generated recipes, using fallback');
        return this.generateFallbackRecipes(description);
      }
    } catch (error) {
      console.error('Error generating recipes:', error);
      return this.generateFallbackRecipes(description);
    }
  }

  private parseGeneratedRecipes(result: any, description: string): Recipe[] {
    // This is a simplified parser - in reality, the free models might not generate perfect JSON
    // So we'll create structured recipes based on the description
    return this.generateFallbackRecipes(description);
  }

  private generateFallbackRecipes(description: string): Recipe[] {
    console.log('Generating recipes based on description:', description);

    // Analyze the description to identify ingredients and cooking methods
    const ingredients = this.extractIngredients(description);
    const cookingStyle = this.determineCookingStyle(description);

    const recipes: Recipe[] = [];

    // Recipe 1: Simple preparation based on detected ingredients
    recipes.push({
      id: '1',
      name: `${cookingStyle.name} with ${ingredients.main}`,
      description: `A delicious ${cookingStyle.name.toLowerCase()} featuring ${ingredients.main}`,
      ingredients: [
        ingredients.main,
        ...ingredients.supporting,
        ...cookingStyle.seasonings
      ],
      instructions: [
        `Prepare and clean the ${ingredients.main}`,
        ...cookingStyle.instructions,
        'Taste and adjust seasoning as needed',
        'Serve hot and enjoy!'
      ],
      prepTime: cookingStyle.prepTime,
      cookTime: cookingStyle.cookTime,
      servings: 4,
      difficulty: 'Easy' as const,
      tags: cookingStyle.tags
    });

    // Recipe 2: Seasoned version
    recipes.push({
      id: '2',
      name: `Seasoned ${description} Delight`,
      description: `A flavorful recipe with herbs and spices`,
      ingredients: [
        description,
        'Mixed herbs (1 tsp)',
        'Paprika (1/2 tsp)',
        'Black pepper (1/4 tsp)',
        'Salt to taste',
        'Butter (2 tbsp)'
      ],
      instructions: [
        'Preheat your cooking surface to medium heat',
        'Season the main ingredients with herbs and spices',
        'Melt butter in the pan',
        'Cook the seasoned ingredients until golden',
        'Adjust seasoning as needed',
        'Serve with your favorite side dish'
      ],
      prepTime: '15 minutes',
      cookTime: '25 minutes',
      servings: 3,
      difficulty: 'Easy' as const,
      tags: ['Flavorful', 'Seasoned', 'Comfort Food']
    });

    // Recipe 3: Creative fusion
    recipes.push({
      id: '3',
      name: `${description} Fusion Bowl`,
      description: `A creative fusion dish with international flavors`,
      ingredients: [
        description,
        'Soy sauce (2 tbsp)',
        'Ginger (1 inch, minced)',
        'Sesame oil (1 tsp)',
        'Green onions (2 stalks)',
        'Rice or noodles for serving'
      ],
      instructions: [
        'Prepare rice or noodles according to package instructions',
        'Heat a wok or large pan over high heat',
        'Add a bit of oil and stir-fry the main ingredients',
        'Add ginger and cook for 1 minute',
        'Add soy sauce and sesame oil, toss to combine',
        'Serve over rice/noodles, garnish with green onions'
      ],
      prepTime: '12 minutes',
      cookTime: '18 minutes',
      servings: 2,
      difficulty: 'Medium' as const,
      tags: ['Fusion', 'Asian-inspired', 'Bowl']
    });

    return recipes;
  }

  private extractIngredients(description: string): { main: string; supporting: string[] } {
    const desc = description.toLowerCase();

    // Common ingredients mapping
    const ingredientMap: { [key: string]: { main: string; supporting: string[] } } = {
      'chicken': {
        main: 'Chicken breast (1 lb)',
        supporting: ['Olive oil (2 tbsp)', 'Garlic (3 cloves)', 'Onion (1 medium)', 'Salt and pepper']
      },
      'beef': {
        main: 'Beef (1 lb)',
        supporting: ['Vegetable oil (2 tbsp)', 'Onion (1 large)', 'Garlic (2 cloves)', 'Salt and pepper']
      },
      'fish': {
        main: 'Fresh fish fillets (1 lb)',
        supporting: ['Lemon (1 whole)', 'Olive oil (3 tbsp)', 'Herbs (fresh)', 'Salt and pepper']
      },
      'vegetables': {
        main: 'Mixed vegetables (2 cups)',
        supporting: ['Olive oil (2 tbsp)', 'Garlic (2 cloves)', 'Salt and pepper', 'Herbs (optional)']
      },
      'pasta': {
        main: 'Pasta (12 oz)',
        supporting: ['Olive oil (3 tbsp)', 'Garlic (3 cloves)', 'Parmesan cheese', 'Salt and pepper']
      },
      'rice': {
        main: 'Rice (2 cups)',
        supporting: ['Chicken broth (4 cups)', 'Onion (1 medium)', 'Garlic (2 cloves)', 'Salt']
      },
      'eggs': {
        main: 'Eggs (6 large)',
        supporting: ['Butter (2 tbsp)', 'Milk (1/4 cup)', 'Salt and pepper', 'Cheese (optional)']
      },
      'bread': {
        main: 'Bread slices (4-6)',
        supporting: ['Butter (3 tbsp)', 'Garlic (2 cloves)', 'Herbs (optional)', 'Salt']
      }
    };

    // Try to match ingredients from description
    for (const [key, value] of Object.entries(ingredientMap)) {
      if (desc.includes(key)) {
        return value;
      }
    }

    // Fallback for unrecognized ingredients
    return {
      main: 'Main ingredient from image',
      supporting: ['Olive oil (2 tbsp)', 'Garlic (2 cloves)', 'Salt and pepper', 'Herbs (optional)']
    };
  }

  private determineCookingStyle(description: string): {
    name: string;
    instructions: string[];
    seasonings: string[];
    prepTime: string;
    cookTime: string;
    tags: string[];
  } {
    const desc = description.toLowerCase();

    if (desc.includes('salad') || desc.includes('fresh') || desc.includes('raw')) {
      return {
        name: 'Fresh Salad',
        instructions: [
          'Wash and chop all fresh ingredients',
          'Combine in a large bowl',
          'Drizzle with olive oil and lemon juice',
          'Toss gently to combine'
        ],
        seasonings: ['Lemon juice (2 tbsp)', 'Extra virgin olive oil (3 tbsp)', 'Salt and pepper'],
        prepTime: '10 minutes',
        cookTime: '0 minutes',
        tags: ['Fresh', 'Healthy', 'No-Cook']
      };
    }

    if (desc.includes('soup') || desc.includes('broth') || desc.includes('liquid')) {
      return {
        name: 'Hearty Soup',
        instructions: [
          'Heat oil in a large pot over medium heat',
          'Sauté aromatics until fragrant',
          'Add main ingredients and broth',
          'Simmer until tender, about 20-25 minutes'
        ],
        seasonings: ['Vegetable broth (4 cups)', 'Bay leaves (2)', 'Thyme (1 tsp)', 'Salt and pepper'],
        prepTime: '15 minutes',
        cookTime: '30 minutes',
        tags: ['Comfort Food', 'Warming', 'Healthy']
      };
    }

    if (desc.includes('fried') || desc.includes('crispy') || desc.includes('golden')) {
      return {
        name: 'Pan-Fried Delight',
        instructions: [
          'Heat oil in a large skillet over medium-high heat',
          'Season the main ingredients',
          'Cook until golden brown on both sides',
          'Reduce heat and cook through'
        ],
        seasonings: ['Vegetable oil (3 tbsp)', 'Paprika (1 tsp)', 'Garlic powder (1 tsp)', 'Salt and pepper'],
        prepTime: '10 minutes',
        cookTime: '15 minutes',
        tags: ['Crispy', 'Golden', 'Satisfying']
      };
    }

    // Default cooking style
    return {
      name: 'Simple Sauté',
      instructions: [
        'Heat olive oil in a pan over medium heat',
        'Add aromatics and cook until fragrant',
        'Add main ingredients and cook until tender',
        'Season and serve'
      ],
      seasonings: ['Olive oil (2 tbsp)', 'Garlic powder (1 tsp)', 'Italian herbs (1 tsp)', 'Salt and pepper'],
      prepTime: '10 minutes',
      cookTime: '20 minutes',
      tags: ['Simple', 'Quick', 'Versatile']
    };
  }

  private fileToBase64(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => {
        const result = reader.result as string;
        resolve(result);
      };
      reader.onerror = reject;
      reader.readAsDataURL(file);
    });
  }
}
