import React, { useState, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import { Upload, X, Camera } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { cn } from '@/lib/utils';

interface ImageUploadProps {
  onImageUpload: (file: File) => void;
  uploadedImage: File | null;
  onRemoveImage: () => void;
  isLoading?: boolean;
}

export const ImageUpload: React.FC<ImageUploadProps> = ({
  onImageUpload,
  uploadedImage,
  onRemoveImage,
  isLoading = false
}) => {
  const [isDragActive, setIsDragActive] = useState(false);

  const onDrop = useCallback((acceptedFiles: File[]) => {
    if (acceptedFiles.length > 0) {
      onImageUpload(acceptedFiles[0]);
    }
  }, [onImageUpload]);

  const { getRootProps, getInputProps } = useDropzone({
    onDrop,
    accept: {
      'image/*': ['.jpeg', '.jpg', '.png', '.webp']
    },
    multiple: false,
    onDragEnter: () => setIsDragActive(true),
    onDragLeave: () => setIsDragActive(false),
    disabled: isLoading
  });

  const imageUrl = uploadedImage ? URL.createObjectURL(uploadedImage) : null;

  return (
    <Card className="relative overflow-hidden bg-gradient-card border-2 border-dashed border-border hover:border-primary/50 transition-all duration-300">
      {uploadedImage ? (
        <div className="relative">
          <img 
            src={imageUrl!} 
            alt="Uploaded fridge contents" 
            className="w-full h-64 object-cover rounded-lg"
          />
          <Button
            variant="destructive"
            size="sm"
            className="absolute top-2 right-2 h-8 w-8 rounded-full"
            onClick={onRemoveImage}
            disabled={isLoading}
          >
            <X className="h-4 w-4" />
          </Button>
          {isLoading && (
            <div className="absolute inset-0 bg-background/80 flex items-center justify-center rounded-lg">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            </div>
          )}
        </div>
      ) : (
        <div
          {...getRootProps()}
          className={cn(
            "p-8 text-center cursor-pointer transition-all duration-300 min-h-64 flex flex-col items-center justify-center",
            isDragActive && "bg-accent/20 border-primary scale-105",
            isLoading && "pointer-events-none opacity-50"
          )}
        >
          <input {...getInputProps()} />
          <div className="mb-4">
            <Camera className="h-12 w-12 mx-auto text-muted-foreground mb-2 animate-bounce-gentle" />
            <Upload className="h-8 w-8 mx-auto text-primary animate-float" />
          </div>
          <h3 className="text-lg font-semibold mb-2 text-foreground">
            Upload Your Fridge Photo
          </h3>
          <p className="text-muted-foreground mb-4 max-w-sm">
            Take a photo of your fridge contents or drag and drop an image here. 
            We'll analyze the ingredients and suggest recipes!
          </p>
          <Button variant="hero" size="lg" className="pointer-events-none">
            Choose Photo
          </Button>
        </div>
      )}
    </Card>
  );
};