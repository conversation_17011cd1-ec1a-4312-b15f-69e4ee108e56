import React, { useEffect } from 'react';
import { ChefHat, Sparkles, Utensils, LogOut, User, ArrowRight, Camera, Zap, Heart } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useAuth } from '@/hooks/useAuth';
import heroImage from '@/assets/hero-kitchen.jpg';

const Index = () => {
  const { user, loading, signOut } = useAuth();
  const navigate = useNavigate();

  const handleSignOut = async () => {
    await signOut();
    navigate('/auth');
  };

  const handleGetStarted = () => {
    if (user) {
      navigate('/recipe');
    } else {
      navigate('/auth');
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary" />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-cream-soft via-background to-orange-light/10">
      {/* Navigation */}
      <nav className="relative z-20 container mx-auto px-4 py-6">
        <div className="flex justify-between items-center">
          <div className="flex items-center gap-2">
            <ChefHat className="h-8 w-8 text-primary" />
            <span className="text-2xl font-bold">Cupboard Chef</span>
          </div>

          {user ? (
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2 text-muted-foreground">
                <User className="h-4 w-4" />
                <span>{user.email}</span>
              </div>
              <Button variant="outline" onClick={handleSignOut} className="gap-2">
                <LogOut className="h-4 w-4" />
                Sign Out
              </Button>
            </div>
          ) : (
            <Button onClick={() => navigate('/auth')} variant="outline">
              Sign In
            </Button>
          )}
        </div>
      </nav>

      {/* Hero Section */}
      <div className="relative overflow-hidden">
        <div
          className="absolute inset-0 bg-cover bg-center opacity-20"
          style={{ backgroundImage: `url(${heroImage})` }}
        />
        <div className="relative z-10 container mx-auto px-4 py-20">
          <div className="text-center max-w-4xl mx-auto">
            <div className="inline-flex items-center gap-2 bg-gradient-warm text-white px-4 py-2 rounded-full text-sm font-medium shadow-warm mb-6 animate-bounce-gentle">
              <Sparkles className="h-4 w-4" />
              AI-Powered Recipe Generator
            </div>

            <h1 className="text-5xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-foreground to-primary bg-clip-text text-transparent">
              Turn Your Ingredients Into Culinary Magic
            </h1>

            <p className="text-xl text-muted-foreground mb-8 leading-relaxed">
              Upload a photo of your fridge contents and let AI create delicious recipes
              tailored to what you have. No more wondering "what's for dinner?"
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
              <Button
                onClick={handleGetStarted}
                size="lg"
                className="gap-2 text-lg px-8 py-6"
              >
                <Camera className="h-5 w-5" />
                Get Started
                <ArrowRight className="h-5 w-5" />
              </Button>
              {!user && (
                <Button
                  onClick={() => navigate('/auth')}
                  variant="outline"
                  size="lg"
                  className="text-lg px-8 py-6"
                >
                  Sign Up Free
                </Button>
              )}
            </div>

            <div className="flex items-center justify-center gap-8 text-muted-foreground">
              <div className="flex items-center gap-2">
                <ChefHat className="h-5 w-5" />
                <span>AI-Generated</span>
              </div>
              <div className="flex items-center gap-2">
                <Zap className="h-5 w-5" />
                <span>Instant Results</span>
              </div>
              <div className="flex items-center gap-2">
                <Heart className="h-5 w-5" />
                <span>Personalized</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Features Section */}
      <div className="container mx-auto px-4 py-20">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">How It Works</h2>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            Transform your ingredients into amazing meals in just three simple steps
          </p>
        </div>

        <div className="grid md:grid-cols-3 gap-8 max-w-5xl mx-auto">
          <Card className="bg-gradient-card shadow-soft border-border/50 text-center">
            <CardHeader>
              <div className="mx-auto w-16 h-16 bg-gradient-warm rounded-full flex items-center justify-center mb-4">
                <Camera className="h-8 w-8 text-white" />
              </div>
              <CardTitle className="text-xl">1. Upload Photo</CardTitle>
            </CardHeader>
            <CardContent>
              <CardDescription className="text-base">
                Take a photo of your fridge, pantry, or ingredients. Our AI can identify what you have available.
              </CardDescription>
            </CardContent>
          </Card>

          <Card className="bg-gradient-card shadow-soft border-border/50 text-center">
            <CardHeader>
              <div className="mx-auto w-16 h-16 bg-gradient-warm rounded-full flex items-center justify-center mb-4">
                <Sparkles className="h-8 w-8 text-white" />
              </div>
              <CardTitle className="text-xl">2. AI Analysis</CardTitle>
            </CardHeader>
            <CardContent>
              <CardDescription className="text-base">
                Our advanced AI analyzes your ingredients and generates personalized recipe suggestions.
              </CardDescription>
            </CardContent>
          </Card>

          <Card className="bg-gradient-card shadow-soft border-border/50 text-center">
            <CardHeader>
              <div className="mx-auto w-16 h-16 bg-gradient-warm rounded-full flex items-center justify-center mb-4">
                <Utensils className="h-8 w-8 text-white" />
              </div>
              <CardTitle className="text-xl">3. Cook & Enjoy</CardTitle>
            </CardHeader>
            <CardContent>
              <CardDescription className="text-base">
                Get detailed recipes with step-by-step instructions and start cooking delicious meals!
              </CardDescription>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* CTA Section */}
      <div className="bg-gradient-warm text-white py-20">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            Ready to Start Cooking?
          </h2>
          <p className="text-xl mb-8 opacity-90 max-w-2xl mx-auto">
            Join thousands of home cooks who are already using AI to create amazing meals from their ingredients.
          </p>
          <Button
            onClick={handleGetStarted}
            size="lg"
            variant="secondary"
            className="gap-2 text-lg px-8 py-6"
          >
            <ChefHat className="h-5 w-5" />
            Start Cooking Now
            <ArrowRight className="h-5 w-5" />
          </Button>
        </div>
      </div>

      {/* Footer */}
      <footer className="bg-muted/30 py-12">
        <div className="container mx-auto px-4 text-center">
          <div className="flex items-center justify-center gap-2 mb-4">
            <ChefHat className="h-6 w-6 text-primary" />
            <span className="text-xl font-bold">Cupboard Chef</span>
          </div>
          <p className="text-muted-foreground">
            Turn your ingredients into culinary magic with AI-powered recipe generation.
          </p>
        </div>
      </footer>
    </div>
  );
};

export default Index;
