import React, { useState, useEffect } from 'react';
import { ChefHat, Sparkles, Utensils, LogOut, User, ArrowLeft } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { ImageUpload } from '@/components/ImageUpload';
import { RecipeCard, Recipe } from '@/components/RecipeCard';
import { RecipeDetail } from '@/components/RecipeDetail';
import { ApiKeyModal } from '@/components/ApiKeyModal';
import { GeminiService, getSampleRecipes } from '@/services/geminiService';
import { HuggingFaceService } from '@/services/huggingFaceService';
import { useAuth } from '@/hooks/useAuth';
import { toast } from 'sonner';
import heroImage from '@/assets/hero-kitchen.jpg';

const Recipe = () => {
  const { user, loading, signOut } = useAuth();
  const navigate = useNavigate();
  const [uploadedImage, setUploadedImage] = useState<File | null>(null);
  const [recipes, setRecipes] = useState<Recipe[]>([]);
  const [selectedRecipe, setSelectedRecipe] = useState<Recipe | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [showApiKeyModal, setShowApiKeyModal] = useState(false);
  const [apiKey, setApiKey] = useState<string>('');
  const [geminiService, setGeminiService] = useState<GeminiService | null>(null);
  const [huggingFaceService] = useState<HuggingFaceService>(new HuggingFaceService());
  const [selectedService, setSelectedService] = useState<'gemini' | 'huggingface'>('huggingface');

  useEffect(() => {
    // Redirect to auth if not logged in
    if (!loading && !user) {
      navigate('/auth');
    }
  }, [user, loading, navigate]);

  useEffect(() => {
    console.log('=== API KEY INITIALIZATION ===');

    // Clear any corrupted API key from localStorage
    const storedApiKey = localStorage.getItem('gemini_api_key');
    if (storedApiKey && (!storedApiKey.startsWith('AIza') || storedApiKey.length < 35)) {
      console.log('Clearing corrupted API key from localStorage:', storedApiKey);
      localStorage.removeItem('gemini_api_key');
    }

    const envApiKey = import.meta.env.VITE_GEMINI_API_KEY;
    console.log('Environment API Key:', envApiKey ? 'Found' : 'Not found');

    const validStoredApiKey = localStorage.getItem('gemini_api_key');
    console.log('Stored API Key:', validStoredApiKey ? 'Found' : 'Not found');

    // Only use keys if they're valid (start with AIza and have proper length)
    const validEnvKey = envApiKey && envApiKey.startsWith('AIza') && envApiKey.length > 35 ? envApiKey : null;
    const validStoredKey = validStoredApiKey && validStoredApiKey.startsWith('AIza') && validStoredApiKey.length > 35 ? validStoredApiKey : null;

    const keyToUse = validStoredKey || validEnvKey;
    console.log('Final API Key available:', !!keyToUse);
    console.log('Final API Key starts with AIza:', keyToUse?.startsWith('AIza'));
    console.log('Using key source:', validStoredKey ? 'localStorage' : validEnvKey ? 'environment' : 'none');

    if (keyToUse) {
      setApiKey(keyToUse);
      setGeminiService(new GeminiService(keyToUse));
      console.log('GeminiService initialized with valid API key');
    } else {
      console.log('No valid API key found - user will need to enter one');
      setApiKey('');
      setGeminiService(null);
    }
  }, []);

  const handleImageUpload = (file: File) => {
    setUploadedImage(file);
    setRecipes([]);
    setSelectedRecipe(null);
  };

  const handleRemoveImage = () => {
    setUploadedImage(null);
    setRecipes([]);
    setSelectedRecipe(null);
  };

  const handleApiKeySubmit = (newApiKey: string) => {
    setApiKey(newApiKey);
    localStorage.setItem('gemini_api_key', newApiKey);
    setGeminiService(new GeminiService(newApiKey));
    toast.success('Gemini API key saved successfully!');
  };

  const testApiConnection = async () => {
    if (!geminiService) {
      toast.error('Gemini service not initialized');
      return;
    }

    try {
      const isWorking = await geminiService.testConnection();
      if (isWorking) {
        toast.success('Gemini API connection successful!');
      } else {
        toast.error('Gemini API connection failed');
      }
    } catch (error) {
      console.error('API test error:', error);
      toast.error('API test failed: ' + (error as Error).message);
    }
  };

  const handleGenerateRecipes = async () => {
    if (!uploadedImage) {
      toast.error('Please upload an image first!');
      return;
    }

    setIsLoading(true);
    try {
      let generatedRecipes: Recipe[];

      if (selectedService === 'huggingface') {
        console.log('Starting recipe generation with Hugging Face (Free)...');
        generatedRecipes = await huggingFaceService.analyzeImageAndGenerateRecipes(uploadedImage);
        toast.success(`Generated ${generatedRecipes.length} delicious recipes using Hugging Face AI (Free)!`);
      } else {
        if (!apiKey || apiKey === '') {
          toast.error('Please set your Gemini API key first!');
          setShowApiKeyModal(true);
          setIsLoading(false);
          return;
        }

        console.log('Starting recipe generation with Gemini...');
        console.log('API Key available:', !!apiKey);
        console.log('Gemini Service available:', !!geminiService);

        generatedRecipes = await geminiService!.analyzeImageAndGenerateRecipes(uploadedImage);
        toast.success(`Generated ${generatedRecipes.length} delicious recipes using Gemini AI!`);
      }

      setRecipes(generatedRecipes);
    } catch (error) {
      console.error('Error generating recipes:', error);

      // Show more detailed error information
      if (error instanceof Error) {
        console.error('Error message:', error.message);
        if (error.message.includes('401') || error.message.includes('invalid') || error.message.includes('API_KEY')) {
          toast.error('Invalid API key. Please check your Gemini API key.');
          setShowApiKeyModal(true);
        } else if (error.message.includes('429') || error.message.includes('quota')) {
          toast.error('🚫 Quota exceeded! You\'ve reached the free tier limit. Try again tomorrow or enable billing for higher limits.');
        } else if (error.message.includes('503') || error.message.includes('overloaded')) {
          toast.error('🔄 Gemini servers are overloaded. Please try again in a few minutes.');
        } else {
          toast.error(`API Error: ${error.message.substring(0, 100)}...`);
        }
      } else {
        toast.error('Failed to generate recipes. Please try again.');
      }

      // Show sample recipes as fallback
      setRecipes(getSampleRecipes());
      toast.info('Showing sample recipes instead. Set a valid Gemini API key for AI-generated recipes.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleTryDemo = () => {
    if (!uploadedImage) {
      toast.error('Please upload an image first!');
      return;
    }

    setIsLoading(true);
    setTimeout(() => {
      setRecipes(getSampleRecipes());
      setIsLoading(false);
      toast.success('Generated sample recipes! Get an API key for personalized suggestions.');
    }, 2000); // Simulate loading
  };

  const handleRecipeSelect = (recipe: Recipe) => {
    setSelectedRecipe(recipe);
  };

  const handleBackToRecipes = () => {
    setSelectedRecipe(null);
  };

  const handleSignOut = async () => {
    await signOut();
    navigate('/auth');
  };

  const handleBackToHome = () => {
    navigate('/');
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary" />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-cream-soft via-background to-orange-light/10">
      {/* Navigation */}
      <nav className="relative z-20 container mx-auto px-4 py-6">
        <div className="flex justify-between items-center">
          <div className="flex items-center gap-4">
            <Button variant="ghost" onClick={handleBackToHome} className="gap-2">
              <ArrowLeft className="h-4 w-4" />
              Back to Home
            </Button>
            <div className="flex items-center gap-2">
              <ChefHat className="h-8 w-8 text-primary" />
              <span className="text-2xl font-bold">Recipe Generator</span>
            </div>
          </div>
          
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2 text-muted-foreground">
              <User className="h-4 w-4" />
              <span>{user?.email}</span>
            </div>
            <Button variant="outline" onClick={handleSignOut} className="gap-2">
              <LogOut className="h-4 w-4" />
              Sign Out
            </Button>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <div className="relative overflow-hidden">
        <div 
          className="absolute inset-0 bg-cover bg-center opacity-20"
          style={{ backgroundImage: `url(${heroImage})` }}
        />
        <div className="relative z-10 container mx-auto px-4 py-16">
          <div className="text-center max-w-4xl mx-auto">
            <div className="inline-flex items-center gap-2 bg-gradient-warm text-white px-4 py-2 rounded-full text-sm font-medium shadow-warm mb-6 animate-bounce-gentle">
              <Sparkles className="h-4 w-4" />
              AI-Powered Recipe Generator
            </div>
            
            <h1 className="text-5xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-foreground to-primary bg-clip-text text-transparent">
              What's Cooking Today?
            </h1>
            
            <p className="text-xl text-muted-foreground mb-8 leading-relaxed">
              Upload a photo of your fridge contents and let AI create delicious recipes 
              tailored to what you have. Turn your ingredients into culinary inspiration!
            </p>

            <div className="flex items-center justify-center gap-6 text-muted-foreground">
              <div className="flex items-center gap-2">
                <ChefHat className="h-5 w-5" />
                <span>AI-Generated</span>
              </div>
              <div className="flex items-center gap-2">
                <Utensils className="h-5 w-5" />
                <span>Personalized</span>
              </div>
              <div className="flex items-center gap-2">
                <Sparkles className="h-5 w-5" />
                <span>Instant Results</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-12">
        {selectedRecipe ? (
          <RecipeDetail 
            recipe={selectedRecipe} 
            onBack={handleBackToRecipes} 
          />
        ) : (
          <div className="max-w-4xl mx-auto space-y-8">
            {/* Upload Section */}
            <Card className="bg-gradient-card shadow-soft border-border/50">
              <CardHeader className="text-center">
                <CardTitle className="text-2xl">Upload Your Fridge Photo</CardTitle>
                <CardDescription>
                  Take a clear photo of your fridge contents or ingredients
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ImageUpload
                  onImageUpload={handleImageUpload}
                  uploadedImage={uploadedImage}
                  onRemoveImage={handleRemoveImage}
                  isLoading={isLoading}
                />
                
                {uploadedImage && (
                  <div className="mt-6 space-y-3">
                    {/* AI Service Selection */}
                    <div className="bg-gray-50 p-4 rounded-lg border">
                      <h3 className="text-sm font-medium text-gray-700 mb-3">Choose AI Service:</h3>
                      <div className="grid grid-cols-2 gap-3">
                        <button
                          onClick={() => setSelectedService('huggingface')}
                          className={`p-3 rounded-lg border text-left transition-all ${
                            selectedService === 'huggingface'
                              ? 'border-green-500 bg-green-50 text-green-700'
                              : 'border-gray-200 bg-white hover:border-gray-300'
                          }`}
                        >
                          <div className="font-medium text-sm">🤗 Hugging Face</div>
                          <div className="text-xs text-gray-500 mt-1">
                            ✅ Completely Free<br/>
                            ✅ No API Key Required<br/>
                            ✅ Unlimited Usage
                          </div>
                        </button>

                        <button
                          onClick={() => setSelectedService('gemini')}
                          className={`p-3 rounded-lg border text-left transition-all ${
                            selectedService === 'gemini'
                              ? 'border-blue-500 bg-blue-50 text-blue-700'
                              : 'border-gray-200 bg-white hover:border-gray-300'
                          }`}
                        >
                          <div className="font-medium text-sm">🔮 Google Gemini</div>
                          <div className="text-xs text-gray-500 mt-1">
                            ⚡ Higher Quality<br/>
                            🔑 API Key Required<br/>
                            📊 Limited Free Quota
                          </div>
                        </button>
                      </div>
                    </div>

                    <Button
                      onClick={handleGenerateRecipes}
                      disabled={isLoading}
                      className="w-full"
                      variant="generate"
                      size="lg"
                    >
                      {isLoading ? (
                        <>
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                          Analyzing Image...
                        </>
                      ) : (
                        <>
                          <Sparkles className="h-4 w-4 mr-2" />
                          Generate Recipes {selectedService === 'huggingface' ? '(Free)' : '(Gemini)'}
                        </>
                      )}
                    </Button>

                    <div className="flex gap-2">
                      <Button
                        onClick={() => setShowApiKeyModal(true)}
                        variant="outline"
                        size="sm"
                        className="flex-1"
                      >
                        Set API Key
                      </Button>
                      <Button
                        onClick={testApiConnection}
                        variant="outline"
                        size="sm"
                        className="flex-1"
                      >
                        Test API
                      </Button>
                      <Button
                        onClick={handleTryDemo}
                        variant="outline"
                        size="sm"
                        className="flex-1"
                        disabled={isLoading}
                      >
                        Try Demo
                      </Button>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Recipes Grid */}
            {recipes.length > 0 && (
              <div className="space-y-6">
                <div className="text-center">
                  <h2 className="text-3xl font-bold mb-2">Your Recipe Suggestions</h2>
                  <p className="text-muted-foreground">
                    Choose a recipe to see the full instructions
                  </p>
                </div>
                
                <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {recipes.map((recipe) => (
                    <RecipeCard
                      key={recipe.id}
                      recipe={recipe}
                      onSelect={handleRecipeSelect}
                    />
                  ))}
                </div>
              </div>
            )}
          </div>
        )}
      </div>

      <ApiKeyModal
        isOpen={showApiKeyModal}
        onClose={() => setShowApiKeyModal(false)}
        onApiKeySubmit={handleApiKeySubmit}
      />
    </div>
  );
};

export default Recipe;
