// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://kvcvobgolwbfhlgtgrog.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imt2Y3ZvYmdvbHdiZmhsZ3Rncm9nIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTM4NjM0NjksImV4cCI6MjA2OTQzOTQ2OX0.8q7D_X4XCbwzklabnj1NoVNQ8GslXjXkRbF_O14AEoY";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY, {
  auth: {
    storage: localStorage,
    persistSession: true,
    autoRefreshToken: true,
  }
});