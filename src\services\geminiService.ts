import { Recipe } from '@/components/RecipeCard';
import { GoogleGenerativeAI } from '@google/generative-ai';

export class GeminiService {
  private genAI: GoogleGenerativeAI;
  private apiKey: string;

  constructor(apiKey: string) {
    this.apiKey = apiKey;
    this.genAI = new GoogleGenerativeAI(apiKey);
    console.log('GeminiService initialized with API key:', apiKey ? `${apiKey.substring(0, 10)}...` : 'None');
  }

  // Test method to verify API key works
  async testConnection(): Promise<boolean> {
    try {
      console.log('Testing Gemini API connection...');
      const model = this.genAI.getGenerativeModel({ model: "gemini-1.0-pro" });
      const result = await model.generateContent("Say hello");
      const response = await result.response;
      const text = response.text();
      console.log('Test connection successful:', text);
      return true;
    } catch (error) {
      console.error('Test connection failed:', error);
      return false;
    }
  }

  async analyzeImageAndGenerateRecipes(imageFile: File): Promise<Recipe[]> {
    const models = ["gemini-1.5-flash", "gemini-1.5-pro"];
    const maxRetries = 1; // Reduce retries to avoid quota exhaustion
    let lastError: Error | null = null;

    for (const modelName of models) {
      for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
          console.log(`=== GEMINI API CALL START (Model: ${modelName}, Attempt ${attempt}/${maxRetries}) ===`);
          console.log('Image file:', imageFile.name, imageFile.type, imageFile.size);
          console.log('Converting image to base64...');
          const base64Image = await this.fileToBase64(imageFile);
          console.log('Image converted successfully, length:', base64Image.length);
          console.log(`Making API call to Gemini ${modelName}...`);

          // Get the generative model
          const model = this.genAI.getGenerativeModel({ model: modelName });
          console.log(`Gemini model initialized (${modelName})`);

          const prompt = `Analyze this image of refrigerator contents and suggest 3-4 recipes that can be made using the visible ingredients. For each recipe, provide:

1. A creative recipe name
2. A brief description (1-2 sentences)
3. Estimated cooking time
4. Number of servings
5. Difficulty level (Easy/Medium/Hard)
6. Complete ingredients list (include quantities)
7. Step-by-step cooking instructions
8. Relevant tags (e.g., "vegetarian", "quick", "comfort food")

Please respond with a valid JSON array of recipes in this exact format:
[
  {
    "id": "recipe1",
    "name": "Recipe Name",
    "description": "Brief description of the dish",
    "cookTime": "30 minutes",
    "servings": 4,
    "difficulty": "Easy",
    "ingredients": ["ingredient 1", "ingredient 2"],
    "instructions": ["step 1", "step 2"],
    "tags": ["tag1", "tag2"]
  }
]

Focus on realistic recipes using ingredients that are clearly visible in the image. Be creative but practical.`;

          // Convert file to the format Gemini expects
          const imagePart = {
            inlineData: {
              data: base64Image.split(',')[1], // Remove data:image/jpeg;base64, prefix
              mimeType: imageFile.type
            }
          };

          console.log('Image part prepared:', {
            mimeType: imagePart.inlineData.mimeType,
            dataLength: imagePart.inlineData.data.length
          });

          console.log('Calling Gemini generateContent...');
          const result = await model.generateContent([prompt, imagePart]);
          console.log('Generate content result received');

          const response = await result.response;
          console.log('Response extracted');

          const content = response.text();
          console.log('Gemini API Response received successfully');
          console.log('Full response content:', content);

          // Extract JSON from the response
          console.log('Extracting JSON from response...');
          const jsonMatch = content.match(/\[[\s\S]*\]/);
          if (!jsonMatch) {
            console.error('No JSON array found in response');
            console.error('Response content was:', content);
            throw new Error('Invalid response format from Gemini - no JSON array found');
          }

          console.log('JSON match found:', jsonMatch[0].substring(0, 200) + '...');
          const recipes: Recipe[] = JSON.parse(jsonMatch[0]);
          console.log('Parsed recipes successfully:', recipes.length, 'recipes');
          console.log('=== GEMINI API CALL SUCCESS ===');
          return recipes;

        } catch (error) {
          lastError = error as Error;
          console.error(`=== GEMINI API CALL ERROR (Model: ${modelName}, Attempt ${attempt}/${maxRetries}) ===`);
          console.error('Error analyzing image with Gemini:', error);

          if (error instanceof Error) {
            console.error('Error message:', error.message);

            // Check if it's a quota exceeded error (429)
            if (error.message.includes('429') || error.message.includes('quota')) {
              console.log(`Quota exceeded for ${modelName}, trying next model...`);
              break; // Don't retry on quota errors, just try next model
            }

            // Check if it's a 503 overload error
            if (error.message.includes('503') || error.message.includes('overloaded')) {
              if (attempt < maxRetries) {
                const delay = Math.pow(2, attempt) * 1000; // Exponential backoff: 2s, 4s
                console.log(`Server overloaded, retrying in ${delay}ms...`);
                await new Promise(resolve => setTimeout(resolve, delay));
                continue; // Try again
              } else {
                console.log(`Max retries reached for ${modelName}, trying next model...`);
                break; // Try next model
              }
            }

            // Check if it's a 404 model not found error
            if (error.message.includes('404') || error.message.includes('not found')) {
              console.log(`Model ${modelName} not available, trying next model...`);
              break; // Try next model
            }
          }

          // If it's not a retryable error, try next model
          break;
        }
      }
    }

    // If we get here, all models and retries failed
    console.error('=== ALL GEMINI MODELS AND RETRIES FAILED ===');
    throw lastError || new Error('All Gemini models are currently unavailable');
  }

  private fileToBase64(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => {
        const result = reader.result as string;
        resolve(result);
      };
      reader.onerror = reject;
      reader.readAsDataURL(file);
    });
  }
}

// Sample recipes for demonstration when API key is not provided
export const getSampleRecipes = (): Recipe[] => [
  {
    id: "sample1",
    name: "Mediterranean Vegetable Pasta",
    description: "A fresh and colorful pasta dish with seasonal vegetables, herbs, and a light olive oil dressing.",
    cookTime: "25 minutes",
    servings: 4,
    difficulty: "Easy",
    ingredients: [
      "400g pasta (penne or fusilli)",
      "2 medium zucchini, diced",
      "1 red bell pepper, sliced",
      "200g cherry tomatoes, halved",
      "3 cloves garlic, minced",
      "1/4 cup olive oil",
      "Fresh basil leaves",
      "100g feta cheese, crumbled",
      "Salt and pepper to taste"
    ],
    instructions: [
      "Cook pasta according to package directions until al dente. Reserve 1/2 cup pasta water before draining.",
      "Heat olive oil in a large skillet over medium-high heat.",
      "Add zucchini and bell pepper, cook for 5-6 minutes until slightly tender.",
      "Add garlic and cherry tomatoes, cook for 2-3 minutes until tomatoes start to soften.",
      "Add drained pasta to the skillet with vegetables.",
      "Toss with fresh basil, feta cheese, and a splash of pasta water if needed.",
      "Season with salt and pepper to taste.",
      "Serve immediately with extra basil and feta on top."
    ],
    tags: ["vegetarian", "mediterranean", "quick", "healthy"]
  },
  {
    id: "sample2",
    name: "Creamy Mushroom Risotto",
    description: "Rich and creamy risotto with mixed mushrooms, perfect for a cozy dinner.",
    cookTime: "45 minutes",
    servings: 3,
    difficulty: "Medium",
    ingredients: [
      "300g Arborio rice",
      "1L warm vegetable stock",
      "300g mixed mushrooms, sliced",
      "1 medium onion, finely diced",
      "3 cloves garlic, minced",
      "1/2 cup white wine",
      "50g butter",
      "2 tbsp olive oil",
      "100g Parmesan cheese, grated",
      "Fresh thyme sprigs",
      "Salt and pepper to taste"
    ],
    instructions: [
      "Heat vegetable stock in a saucepan and keep warm.",
      "In a large skillet, sauté mushrooms in 1 tbsp olive oil until golden. Set aside.",
      "In the same pan, heat remaining oil and half the butter. Add onion and cook until soft.",
      "Add garlic and rice, stirring for 2 minutes until rice is lightly toasted.",
      "Pour in white wine and stir until absorbed.",
      "Add warm stock one ladle at a time, stirring continuously until absorbed before adding more.",
      "Continue for 18-20 minutes until rice is creamy and al dente.",
      "Stir in cooked mushrooms, remaining butter, Parmesan, and thyme.",
      "Season with salt and pepper, serve immediately."
    ],
    tags: ["vegetarian", "comfort food", "creamy", "italian"]
  },
  {
    id: "sample3",
    name: "Asian-Style Stir Fry",
    description: "Quick and flavorful stir fry with fresh vegetables and a savory sauce.",
    cookTime: "15 minutes",
    servings: 2,
    difficulty: "Easy",
    ingredients: [
      "2 cups mixed vegetables (broccoli, carrots, snap peas)",
      "2 tbsp vegetable oil",
      "3 cloves garlic, minced",
      "1 tbsp fresh ginger, grated",
      "2 green onions, sliced",
      "3 tbsp soy sauce",
      "1 tbsp oyster sauce",
      "1 tsp sesame oil",
      "1 tsp cornstarch",
      "2 tbsp water",
      "Cooked rice for serving"
    ],
    instructions: [
      "Mix soy sauce, oyster sauce, sesame oil, cornstarch, and water in a small bowl. Set aside.",
      "Heat vegetable oil in a large wok or skillet over high heat.",
      "Add garlic and ginger, stir-fry for 30 seconds until fragrant.",
      "Add mixed vegetables, stir-fry for 3-4 minutes until crisp-tender.",
      "Pour the sauce mixture over vegetables and toss to coat.",
      "Cook for 1-2 minutes until sauce thickens.",
      "Garnish with green onions and serve immediately over rice."
    ],
    tags: ["vegetarian", "quick", "asian", "healthy", "stir-fry"]
  }
];
