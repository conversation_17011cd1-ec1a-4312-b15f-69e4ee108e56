import React from 'react';
import { ArrowLeft, Clock, Users, ChefHat, CheckCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Recipe } from './RecipeCard';

interface RecipeDetailProps {
  recipe: Recipe;
  onBack: () => void;
}

export const RecipeDetail: React.FC<RecipeDetailProps> = ({ recipe, onBack }) => {
  const [checkedIngredients, setCheckedIngredients] = React.useState<Set<number>>(new Set());
  const [checkedSteps, setCheckedSteps] = React.useState<Set<number>>(new Set());

  const toggleIngredient = (index: number) => {
    const newChecked = new Set(checkedIngredients);
    if (newChecked.has(index)) {
      newChecked.delete(index);
    } else {
      newChecked.add(index);
    }
    setCheckedIngredients(newChecked);
  };

  const toggleStep = (index: number) => {
    const newChecked = new Set(checkedSteps);
    if (newChecked.has(index)) {
      newChecked.delete(index);
    } else {
      newChecked.add(index);
    }
    setCheckedSteps(newChecked);
  };

  const difficultyColors = {
    Easy: 'bg-green-fresh text-green-fresh-foreground',
    Medium: 'bg-orange-light text-orange-warm',
    Hard: 'bg-destructive text-destructive-foreground'
  };

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      <div className="flex items-center gap-4">
        <Button
          variant="ghost"
          size="sm"
          onClick={onBack}
          className="hover:bg-accent"
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Recipes
        </Button>
      </div>

      <Card className="bg-gradient-card shadow-soft border-border/50">
        <CardHeader className="pb-6">
          <div className="flex flex-col md:flex-row md:items-start justify-between gap-4">
            <div className="flex-1">
              <CardTitle className="text-3xl mb-3">{recipe.name}</CardTitle>
              <CardDescription className="text-lg text-muted-foreground">
                {recipe.description}
              </CardDescription>
            </div>
            <Badge 
              className={`${difficultyColors[recipe.difficulty]} border-0 shadow-soft px-3 py-1`}
            >
              {recipe.difficulty}
            </Badge>
          </div>

          <div className="flex flex-wrap items-center gap-6 mt-6 text-muted-foreground">
            <div className="flex items-center gap-2">
              <Clock className="h-5 w-5" />
              <span>{recipe.cookTime}</span>
            </div>
            <div className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              <span>{recipe.servings} servings</span>
            </div>
            <div className="flex items-center gap-2">
              <ChefHat className="h-5 w-5" />
              <span>{recipe.ingredients.length} ingredients</span>
            </div>
          </div>

          <div className="flex flex-wrap gap-2 mt-4">
            {recipe.tags.map((tag) => (
              <Badge 
                key={tag} 
                variant="secondary" 
                className="bg-accent/50 text-accent-foreground border-0"
              >
                {tag}
              </Badge>
            ))}
          </div>
        </CardHeader>

        <CardContent className="space-y-8">
          <div>
            <h3 className="text-xl font-semibold mb-4 flex items-center gap-2">
              <ChefHat className="h-5 w-5" />
              Ingredients
            </h3>
            <div className="grid gap-3">
              {recipe.ingredients.map((ingredient, index) => (
                <div
                  key={index}
                  className="flex items-center gap-3 p-3 rounded-lg bg-accent/20 hover:bg-accent/30 transition-colors cursor-pointer"
                  onClick={() => toggleIngredient(index)}
                >
                  <CheckCircle 
                    className={`h-5 w-5 transition-colors ${
                      checkedIngredients.has(index) 
                        ? 'text-primary fill-current' 
                        : 'text-muted-foreground'
                    }`} 
                  />
                  <span 
                    className={`flex-1 transition-all ${
                      checkedIngredients.has(index) 
                        ? 'line-through text-muted-foreground' 
                        : 'text-foreground'
                    }`}
                  >
                    {ingredient}
                  </span>
                </div>
              ))}
            </div>
          </div>

          <Separator />

          <div>
            <h3 className="text-xl font-semibold mb-4">Instructions</h3>
            <div className="space-y-4">
              {recipe.instructions.map((step, index) => (
                <div
                  key={index}
                  className="flex gap-4 p-4 rounded-lg bg-accent/20 hover:bg-accent/30 transition-colors cursor-pointer"
                  onClick={() => toggleStep(index)}
                >
                  <div className="flex-shrink-0">
                    <div className={`
                      w-8 h-8 rounded-full flex items-center justify-center text-sm font-semibold transition-colors
                      ${checkedSteps.has(index) 
                        ? 'bg-primary text-primary-foreground' 
                        : 'bg-muted text-muted-foreground'
                      }
                    `}>
                      {index + 1}
                    </div>
                  </div>
                  <div className="flex-1">
                    <p className={`
                      transition-all leading-relaxed
                      ${checkedSteps.has(index) 
                        ? 'line-through text-muted-foreground' 
                        : 'text-foreground'
                      }
                    `}>
                      {step}
                    </p>
                  </div>
                  <CheckCircle 
                    className={`h-5 w-5 transition-colors flex-shrink-0 mt-1 ${
                      checkedSteps.has(index) 
                        ? 'text-primary fill-current' 
                        : 'text-muted-foreground'
                    }`} 
                  />
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};