import React, { useState } from 'react';
import { Key, ExternalLink } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

interface ApiKeyModalProps {
  isOpen: boolean;
  onClose: () => void;
  onApiKeySubmit: (apiKey: string) => void;
}

export const ApiKeyModal: React.FC<ApiKeyModalProps> = ({
  isOpen,
  onClose,
  onApiKeySubmit,
}) => {
  const [apiKey, setApiKey] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!apiKey.trim()) return;

    setIsLoading(true);
    try {
      onApiKeySubmit(apiKey.trim());
      onClose();
    } catch (error) {
      console.error('Error with API key:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md bg-gradient-card">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Key className="h-5 w-5" />
            Gemini API Key Required
          </DialogTitle>
          <DialogDescription>
            To analyze your fridge contents and generate recipes, please enter your Google Gemini API key.
            Your key is stored locally and never sent to our servers.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="apiKey">API Key</Label>
            <Input
              id="apiKey"
              type="password"
              placeholder="AIza..."
              value={apiKey}
              onChange={(e) => setApiKey(e.target.value)}
              className="bg-background/50"
            />
          </div>

          <div className="flex flex-col gap-3">
            <Button
              type="submit"
              disabled={!apiKey.trim() || isLoading}
              className="w-full"
              variant="hero"
            >
              {isLoading ? 'Saving...' : 'Save API Key'}
            </Button>
            
            <Button
              type="button"
              variant="outline"
              onClick={() => window.open('https://aistudio.google.com/app/apikey', '_blank')}
              className="w-full"
            >
              <ExternalLink className="h-4 w-4 mr-2" />
              Get Gemini API Key
            </Button>
          </div>
        </form>

        <div className="text-xs text-muted-foreground mt-4 p-3 bg-accent/20 rounded-lg">
          <strong>Note:</strong> You can also try the demo with sample recipes by clicking "Skip for now" 
          if you don't have an API key yet.
        </div>

        <Button
          variant="ghost"
          onClick={onClose}
          className="w-full mt-2"
        >
          Skip for now (use demo)
        </Button>
      </DialogContent>
    </Dialog>
  );
};